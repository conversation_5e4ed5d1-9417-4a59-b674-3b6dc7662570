#!/usr/bin/env python 
# -*- coding:utf-8 -*-
# comfyui api 工作流
#

import websocket 
import uuid
import json
import urllib.request
import urllib.parse
import argparse
import os
import base64
import io
from PIL import Image, PngImagePlugin
from requests_toolbelt import MultipartEncoder
import random
from ..config import settings 
import zlib 
import logging
import pprint 
import piexif

import asyncio

from ..utils.kontext_presets import KontextMap

logger = logging.getLogger()

def encode_pil_to_base64(image, ext:str):

    with io.BytesIO() as output_bytes:
        if ext.lower() == 'png':
            metadata = None
            for key, value in image.info.items():
                if isinstance(key, str) and isinstance(value, str):
                    if metadata is None:
                        metadata = PngImagePlugin.PngInfo()
                    metadata.add_text(key, value)
            image.save(output_bytes, format="PNG", pnginfo=metadata)
        elif ext.lower() in ("jpg", "jpeg"):
            parameters = image.info.get('parameters', None)
            exif_bytes = piexif.dump({
                "Exif": { piexif.ExifIFD.UserComment: piexif.helper.UserComment.dump(parameters or "", encoding="unicode") }
            })
            image.save(output_bytes, format="JPEG", exif = exif_bytes, quality=80)
        elif ext.lower() == "webp":
            parameters = image.info.get('parameters', None)
            exif_bytes = piexif.dump({
                "Exif": { piexif.ExifIFD.UserComment: piexif.helper.UserComment.dump(parameters or "", encoding="unicode") }
            })
            image.save(output_bytes, format="WEBP", exif = exif_bytes, method=6, quality=60, alpha_quality=50, save_all=True)

        bytes_data = output_bytes.getvalue()

    return str(base64.b64encode(bytes_data), encoding="utf-8")

def get_image_format_content_type(filename):
  # Detect image MIME type based on file extension
    mime_type = "image/png"  # Default to PNG
    mime_format = "PNG"

    if filename.lower().endswith('.jpg') or filename.lower().endswith('.jpeg'):
        mime_type = 'image/jpeg'
        mime_format = 'JPEG'
    elif filename.lower().endswith('.webp'):
        mime_type = 'image/webp'
        mime_format = 'WEBP'
    return [mime_type, mime_format]

async def async_wrapper(func, *args):
    loop = asyncio.get_event_loop()
    result = await loop.run_in_executor(None, func, *args)
    return result

def open_websocket_connection():
  server_address= settings.comfy_base_url
  client_id=str(uuid.uuid4())
  ws = websocket.WebSocket()
  ws.connect("ws://{}/ws?clientId={}".format(server_address, client_id))
  return ws, server_address, client_id

def queue_prompt(prompt, client_id, server_address):
  
  p = {"prompt": prompt, "client_id": client_id, 
       "extra_data":{
        "api_key_comfy_org": "comfyui-ca3a7f143790214a805909a10a7724b62fd59bff31ba1000e78558952265745a"
        }
    }
  headers = {'Content-Type': 'application/json'}
  data = json.dumps(p).encode('utf-8')
  req =  urllib.request.Request("http://{}/prompt".format(server_address), data=data, headers=headers)
  return json.loads(urllib.request.urlopen(req).read())

def get_history(prompt_id, server_address):
  with urllib.request.urlopen("http://{}/history/{}".format(server_address, prompt_id)) as response:
      return json.loads(response.read())
  
def get_image(filename, subfolder, folder_type, server_address):
  data = {"filename": filename, "subfolder": subfolder, "type": folder_type}
  url_values = urllib.parse.urlencode(data)

  with urllib.request.urlopen("http://{}/view?{}".format(server_address, url_values)) as response:
      return response.read()

def upload_image(input_path, name, server_address, image_type="input", overwrite=False):
  
  (mime_type, _) = get_image_format_content_type(input_path)

  with open(input_path, 'rb') as file:
    multipart_data = MultipartEncoder(
      fields= {
        'image': (name, file, mime_type),
        'type': image_type,
        'overwrite': str(overwrite).lower()
      }
    )

    data = multipart_data
    headers = { 'Content-Type': multipart_data.content_type }
    request = urllib.request.Request("http://{}/upload/image".format(server_address), data=data, headers=headers)
    with urllib.request.urlopen(request) as response:
      return response.read()

def load_workflow(workflow_path):
  try:
      with open(workflow_path, 'r', encoding="utf-8") as file:
          workflow = json.load(file)
          return json.dumps(workflow)
  except FileNotFoundError:
      print(f"The file {workflow_path} was not found.")
      return None
  except json.JSONDecodeError:
      print(f"The file {workflow_path} contains invalid JSON.")
      return None
  
def track_progress(prompt, ws, prompt_id):
    node_ids = list(prompt.keys())
    finished_nodes = []

    while True:
        out = ws.recv()
        if isinstance(out, str):
            message = json.loads(out)
            if message['type'] == 'progress':
                data = message['data']
                current_step = data['value']
                print('In K-Sampler -> Step: ', current_step, ' of: ', data['max'])
            if message['type'] == 'execution_cached':
                data = message['data']
                for itm in data['nodes']:
                    if itm not in finished_nodes:
                        finished_nodes.append(itm)
                        print('Progess: ', len(finished_nodes), '/', len(node_ids), ' Tasks done')
            if message['type'] == 'executing':
                data = message['data']
                if data['node'] not in finished_nodes:
                    finished_nodes.append(data['node'])
                    print('Progess: ', len(finished_nodes), '/', len(node_ids), ' Tasks done')

                if data['node'] is None and data['prompt_id'] == prompt_id:
                    break #Execution is done
        else:
            continue
    return

def get_images(prompt_id, server_address, allow_preview = False):
    output_images = []
    history = get_history(prompt_id, server_address)[prompt_id]
    
    for node_id in history['outputs']:
        
        node_output = history['outputs'][node_id]
        output_data = {}
        if 'images' in node_output:
            for image in node_output['images']:
                if image['type'] == 'output':
                    image_data = get_image(image['filename'], image['subfolder'], image['type'], server_address)
                    output_data['image_data'] = image_data
                    output_data['file_name'] = image['filename']
                    output_data['type'] = image['type']
        output_images.append(output_data)
        
    return [ v for v in output_images if 'image_data' in v and  v['image_data'] != None ]
    
async def comfy_image(img, model_style):

    workflow = load_workflow(os.path.join(settings.comfy_dir, f"{model_style}.json"))    
    prompt = json.loads(workflow)
    id_to_class_type = {id: details['class_type'] for id, details in prompt.items()}
    seedUpdate = [key for key, value in id_to_class_type.items() if value.find('KSampler') >= 0 or value.find('FaceDetailer') >= 0 or value.find("Text Random Line") >= 0]

    for seedData in seedUpdate:
        #prompt.get(seedData)['inputs']['seed'] = zlib.crc32(img.encode("utf-8"))
        prompt.get(seedData)['inputs']['seed'] = random.randint(0, 1000000000)
        
    image_loader = [key for key, value in id_to_class_type.items() if value == 'LoadImage'][0]
    filename = os.path.split(img)[-1]
    prompt.get(image_loader)['inputs']['image'] = filename
    
    ws, server_address, client_id = open_websocket_connection()
    try:
        logger.info(f"process request {model_style}")
        upload_image(img, filename, server_address)
        prompt_id = queue_prompt(prompt, client_id, server_address)['prompt_id']
        track_progress(prompt, ws, prompt_id)
        images = get_images(prompt_id, server_address, False)
        image_content = images[-1]
        (mime_type, mime_format) = get_image_format_content_type(image_content["file_name"])
        img = Image.open(io.BytesIO(image_content['image_data']))
        logger.info(f"process request finished. {model_style}")
        return [img, mime_type, mime_format]
      
    finally:
        ws.close()

async def comfy_edit(img, model_name, prompt, style_lora, factor, detail, seed, repaint):

    workflow = load_workflow(os.path.join(settings.comfy_dir, f"{model_name}.json"))    
    workflow = json.loads(workflow)
    
    id_to_class_type = {id: details['class_type'] for id, details in workflow.items()}
    id_to_title = {id: details['_meta']["title"] for id, details in workflow.items()}
    
    seedUpdate = [key for key, value in id_to_class_type.items() if value.find('KSampler') >= 0 or value.find('FaceDetailer') >= 0 or value.find("Text Random Line") >= 0]
        
    for seedData in seedUpdate:
        # workflow.get(seedData)['inputs']['seed'] = zlib.crc32(img.encode("utf-8"))
        if seed == -1:
            seed = random.randint(0, 1000000000)
            
        workflow.get(seedData)['inputs']['seed'] = seed
        
    noiseSeed = [key for key, value in id_to_class_type.items() if value.find("RandomNoise") >= 0]
    for seedData in noiseSeed:
        if seed == -1:
            seed = random.randint(0, 1000000000)
        workflow.get(seedData)['inputs']['noise_seed'] = seed

    basicScheduler = [key for key, value in id_to_class_type.items() if value.find("BasicScheduler") >= 0]
    for scheduler in basicScheduler:
        workflow.get(scheduler)['inputs']['denoise'] = repaint
    
    image_loader = [key for key, value in id_to_title.items() if value == 'InputImage'][0]
    filename = os.path.split(img)[-1]
    workflow.get(image_loader)['inputs']['image'] = filename
    lora_loader = [key for key, value in id_to_title.items() if value == 'StyleLora'][0]
    workflow.get(lora_loader)['inputs']['string'] = style_lora
    
    aspect_loader = [key for key, value in id_to_title.items() if value == 'AspectRatio'][0]
    workflow.get(aspect_loader)['inputs']['aspect_ratio'] = factor
    detail_loader = [key for key, value in id_to_title.items() if value == 'PictureDetail'][0]
    workflow.get(detail_loader)['inputs']['guidance'] = detail
    prompt_loader = [key for key, value in id_to_title.items() if value == 'UserPrompt'][0]
    workflow.get(prompt_loader)['inputs']['text'] = prompt
    
    ws, server_address, client_id = open_websocket_connection()
    try:
        logger.info(f"process request {model_name}, {prompt}, {style_lora}, {factor}, {detail}, {seed}, {repaint}")
        upload_image(img, filename, server_address)
        prompt_id = queue_prompt(workflow, client_id, server_address)['prompt_id']
        track_progress(workflow, ws, prompt_id)
        images = get_images(prompt_id, server_address, False)
        image_content = images[-1]
        (mime_type, mime_format) = get_image_format_content_type(image_content["file_name"])
        img = Image.open(io.BytesIO(image_content['image_data']))

        logger.info(f"process request finished. {model_name}, {prompt}, {style_lora}, {factor}, {detail}, {seed}, {repaint}")
        return [img, mime_type, mime_format]
      
    finally:
        ws.close()

async def comfy_refer(img, model_name, prompt, style_lora, factor, detail, seed, repaint):

    workflow = load_workflow(os.path.join(settings.comfy_dir, f"{model_name}.json"))    
    workflow = json.loads(workflow)
    
    id_to_class_type = {id: details['class_type'] for id, details in workflow.items()}
    id_to_title = {id: details['_meta']["title"] for id, details in workflow.items()}
    
    seedUpdate = [key for key, value in id_to_class_type.items() if value.find('KSampler') >= 0 or value.find('FaceDetailer') >= 0 or value.find("Text Random Line") >= 0]
        
    for seedData in seedUpdate:
        # workflow.get(seedData)['inputs']['seed'] = zlib.crc32(img.encode("utf-8"))
        if seed == -1:
            seed = random.randint(0, 1000000000)
            
        workflow.get(seedData)['inputs']['seed'] = seed
        
    noiseSeed = [key for key, value in id_to_class_type.items() if value.find("RandomNoise") >= 0]
    for seedData in noiseSeed:
        if seed == -1:
            seed = random.randint(0, 1000000000)
        workflow.get(seedData)['inputs']['noise_seed'] = seed

    basicScheduler = [key for key, value in id_to_title.items() if value.find("BasicScheduler") >= 0]
    for scheduler in basicScheduler:
        workflow.get(scheduler)['inputs']['denoise'] = repaint
    
    image_loader = [key for key, value in id_to_title.items() if value == 'InputImage'][0]
    filename = os.path.split(img)[-1]
    workflow.get(image_loader)['inputs']['image'] = filename
    template_loader = [key for key, value in id_to_title.items() if value == 'LoadImageFromPath'][0]
    workflow.get(template_loader)['inputs']['image'] = f"input/template/{style_lora}.png"
    
    aspect_loader = [key for key, value in id_to_title.items() if value == 'AspectRatio'][0]
    workflow.get(aspect_loader)['inputs']['aspect_ratio'] = factor

    prompt_loader = [key for key, value in id_to_title.items() if value == 'UserPrompt'][0]
    workflow.get(prompt_loader)['inputs']['text'] = prompt
    
    ws, server_address, client_id = open_websocket_connection()
    try:
        logger.info(f"process request {model_name}, {prompt}, {style_lora}, {factor}, {detail}, {seed}, {repaint}")
        upload_image(img, filename, server_address)
        prompt_id = queue_prompt(workflow, client_id, server_address)['prompt_id']
        track_progress(workflow, ws, prompt_id)
        images = get_images(prompt_id, server_address, False)
        image_content = images[-1]
        (mime_type, mime_format) = get_image_format_content_type(image_content["file_name"])

        img = Image.open(io.BytesIO(image_content['image_data']))
        logger.info(f"process request finished. {model_name}, {prompt}, {style_lora}, {factor}, {detail}, {seed}, {repaint}")
     
        return [img, mime_type, mime_format]
      
    finally:
        ws.close()

async def comfy_draw(img, model_name, prompt, style_lora, factor, detail, seed, repaint):
    template = model_name.split('_')[-1]
    if int(template) >= 65 and int(template) <= 91:
        model_name = f"refer_65"

    workflow = load_workflow(os.path.join(settings.comfy_dir, f"{model_name}.json"))    
    workflow = json.loads(workflow)
    
    id_to_class_type = {id: details['class_type'] for id, details in workflow.items()}
    id_to_title = {id: details['_meta']["title"] for id, details in workflow.items()}
    #model_name refer_55
    if not model_name in ["refer_55"]:
        seedUpdate = [key for key, value in id_to_class_type.items() if value.find('KSampler') >= 0 or value.find('FaceDetailer') >= 0 or value.find("Text Random Line") >= 0]
            
        for seedData in seedUpdate:
            # workflow.get(seedData)['inputs']['seed'] = zlib.crc32(img.encode("utf-8"))
            if seed == -1:
                seed = random.randint(0, 1000000000)
                
            workflow.get(seedData)['inputs']['seed'] = seed
            
        noiseSeed = [key for key, value in id_to_class_type.items() if value.find("RandomNoise") >= 0]
        for seedData in noiseSeed:
            if seed == -1:
                seed = random.randint(0, 1000000000)
            workflow.get(seedData)['inputs']['noise_seed'] = seed
    
    image_loaders = [key for key, value in id_to_title.items() if value == 'InputImage']

    for image_loader in image_loaders:
        filename = os.path.split(img)[-1]
        workflow.get(image_loader)['inputs']['image'] = filename
    
    aspect_loaders = [key for key, value in id_to_title.items() if value == 'AspectRatio']
    for aspect_loader in aspect_loaders:
        workflow.get(aspect_loader)['inputs']['aspect_ratio'] = factor

    prompt_loaders = [key for key, value in id_to_title.items() if value == 'UserPrompt']
    for prompt_loader in prompt_loaders:
        workflow.get(prompt_loader)['inputs']['text'] = prompt
    
    # kontext presets replace
    kontext_presets = [key for key, value in id_to_title.items() if value == 'KontextPresets']
    for kontext_loader in kontext_presets:
        workflow.get(kontext_loader)['inputs']['preset'] = KontextMap[template]

    ws, server_address, client_id = open_websocket_connection()

    try:
        logger.info(f"process request {model_name}, {prompt}, {style_lora}, {factor}, {detail}, {seed}, {repaint}")
        upload_image(img, filename, server_address)
        prompt_id = queue_prompt(workflow, client_id, server_address)['prompt_id']
        track_progress(workflow, ws, prompt_id)
        images = get_images(prompt_id, server_address, False)
        image_content = images[-1]
        (mime_type, mime_format) = get_image_format_content_type(image_content["file_name"])

        img = Image.open(io.BytesIO(image_content['image_data']))
        logger.info(f"process request finished. {model_name}, {prompt}, {style_lora}, {factor}, {detail}, {seed}, {repaint}")
     
        return [img, mime_type, mime_format]
      
    finally:
        ws.close()

async def comfy_t2i(img, model_name, prompt, style_lora, factor, detail, seed, repaint):

    workflow = load_workflow(os.path.join(settings.comfy_dir, f"{model_name}.json"))    
    workflow = json.loads(workflow)
    
    id_to_title = {id: details['_meta']["title"] for id, details in workflow.items()}

    aspect_loaders = [key for key, value in id_to_title.items() if value == 'AspectRatio']
    for aspect_loader in aspect_loaders:
        workflow.get(aspect_loader)['inputs']['aspect_ratio'] = factor

    prompt_loaders = [key for key, value in id_to_title.items() if value == 'UserPrompt']
    for prompt_loader in prompt_loaders:
        workflow.get(prompt_loader)['inputs']['text'] = prompt
    
    ws, server_address, client_id = open_websocket_connection()
    try:
        logger.info(f"process request {model_name}, {prompt}, {style_lora}, {factor}, {detail}, {seed}, {repaint}")
        prompt_id = queue_prompt(workflow, client_id, server_address)['prompt_id']
        track_progress(workflow, ws, prompt_id)
        images = get_images(prompt_id, server_address, False)
        image_content = images[-1]
        (mime_type, mime_format) = get_image_format_content_type(image_content["file_name"])

        img = Image.open(io.BytesIO(image_content['image_data']))
        logger.info(f"process request finished. {model_name}, {prompt}, {style_lora}, {factor}, {detail}, {seed}, {repaint}")
     
        return [img, mime_type, mime_format]
      
    finally:
        ws.close()

async def comfy_wan(img, model_name, prompt, style_lora, factor, detail, seed, repaint):

    workflow = load_workflow(os.path.join(settings.comfy_dir, f"{model_name}.json"))    
    workflow = json.loads(workflow)
    
    id_to_class_type = {id: details['class_type'] for id, details in workflow.items()}
    id_to_title = {id: details['_meta']["title"] for id, details in workflow.items()}
    
    seedUpdate = [key for key, value in id_to_class_type.items() if value.find('KSampler') >= 0 or value.find('FaceDetailer') >= 0 or value.find("Text Random Line") >= 0]
        
    for seedData in seedUpdate:
        # workflow.get(seedData)['inputs']['seed'] = zlib.crc32(img.encode("utf-8"))
        if seed == -1:
            seed = random.randint(0, 1000000000)    
        workflow.get(seedData)['inputs']['seed'] = seed
    
    image_loader = [key for key, value in id_to_title.items() if value == 'InputImage'][0]
    filename = os.path.split(img)[-1]
    workflow.get(image_loader)['inputs']['image'] = filename
    
    prompt_loader = [key for key, value in id_to_title.items() if value == 'UserPrompt']
    if len(prompt_loader) > 0:
        workflow.get(prompt_loader[0])['inputs']['text'] = prompt
 
    
    ws, server_address, client_id = open_websocket_connection()
    try:
        logger.info(f"process request {model_name}, {prompt}, {style_lora}, {factor}, {detail}, {seed}, {repaint}")
        upload_image(img, filename, server_address)
        prompt_id = queue_prompt(workflow, client_id, server_address)['prompt_id']
        track_progress(workflow, ws, prompt_id)
        images = get_images(prompt_id, server_address, False)
        image_content = images[-1]
        (mime_type, mime_format) = get_image_format_content_type(image_content["file_name"])

        img = Image.open(io.BytesIO(image_content['image_data']))
        logger.info(f"process request finished. {model_name}, {prompt}, {style_lora}, {factor}, {detail}, {seed}, {repaint}")
     
        return [img, mime_type, mime_format]
      
    finally:
        ws.close()
        
async def comfy_webp(img, model_name) -> Image:

    workflow = load_workflow(os.path.join(settings.comfy_dir, f"{model_name}.json"))    
    workflow = json.loads(workflow)
    
    id_to_class_type = {id: details['class_type'] for id, details in workflow.items()}
    id_to_title = {id: details['_meta']["title"] for id, details in workflow.items()}
    

    image_loader = [key for key, value in id_to_title.items() if value == 'InputImage'][0]
    filename = os.path.split(img)[-1]
    workflow.get(image_loader)['inputs']['image'] = filename

    
    ws, server_address, client_id = open_websocket_connection()
    try:
        upload_image(img, filename, server_address)
        prompt_id = queue_prompt(workflow, client_id, server_address)['prompt_id']
        track_progress(workflow, ws, prompt_id)
        images = get_images(prompt_id, server_address, False)
        image_content = images[-1]
        (mime_type, mime_format) = get_image_format_content_type(image_content["file_name"])

        img = Image.open(io.BytesIO(image_content['image_data']))
        return [img, mime_type, mime_format]
      
    finally:
        ws.close()


