# Nieta Art 爬虫完善总结

## 🎯 完成的功能

### 1. 分层目录结构
- ✅ 更新存储路径为 `dataset/nieta/category/character`
- ✅ 自动创建分类和角色的嵌套目录
- ✅ 文件名清理，处理特殊字符

### 2. 角色卡片交互
- ✅ 点击角色卡片进入详情页面
- ✅ 自动提取角色UUID
- ✅ 处理页面导航和返回

### 3. 创作图片下载
- ✅ 访问角色创作页面 (`https://app.nieta.art/oc?uuid=xxx`)
- ✅ 滚动加载更多图片
- ✅ 批量下载前100张创作图片
- ✅ 智能文件命名 (`creation_001.jpg`, `creation_002.png`, ...)

### 4. 增强的数据结构
- ✅ 添加UUID字段
- ✅ 添加创作图片数量统计
- ✅ 分离头像和创作图片路径
- ✅ 增强的错误处理

## 📁 目录结构示例

```
dataset/nieta/
├── 动漫_游戏/
│   ├── 可爱女孩/
│   │   ├── avatar.jpg
│   │   ├── creation_001.jpg
│   │   ├── creation_002.png
│   │   └── ...
│   └── 机器人战士/
│       ├── avatar.png
│       └── creation_001.jpg
├── 科幻_未来/
│   └── 太空探险家/
│       ├── avatar.jpg
│       ├── creation_001.jpg
│       └── ...
└── nieta_all_characters.json
```

## 🔧 技术实现

### 新增函数
1. **`extract_uuid_from_url(url)`**: 从URL中提取UUID
2. **`get_character_creation_images(page, session, uuid, dir, max_images=100)`**: 下载创作图片
3. **`process_character_details(page, session, characters, category)`**: 处理角色详情

### 核心改进
- 使用正则表达式提取UUID
- 异步图片下载和文件写入
- 智能滚动加载机制
- 完善的错误处理和重试逻辑

## 📊 统计信息

爬虫现在提供以下统计：
- 各分类角色数量
- 头像下载成功/失败数量
- 创作图片总数
- UUID获取成功/失败数量

## 🚀 使用方法

### 快速启动
```bash
python run_nieta_crawler.py
```

### 直接运行
```bash
python spider/nieta_art_crawler.py
```

## ⚙️ 配置选项

在 `spider/nieta_art_crawler.py` 中可以调整：

```python
# 数据集目录
DATASET_DIR = "dataset/nieta"

# 爬取分类范围 (跳过前4个空分类)
categories[4:9]

# 每个角色最大创作图片数
max_images = 100

# 创作页面最大滚动次数
max_scroll_attempts = 10
```

## 🔍 JSON 数据格式

```json
{
  "category": "原始分类名",
  "clean_category": "清理后分类名",
  "name": "原始角色名",
  "clean_name": "清理后角色名",
  "image_url": "头像URL",
  "heat_score": 热度分数,
  "character_dir": "角色目录路径",
  "avatar_path": "头像文件路径",
  "download_success": true,
  "creation_images_count": 15,
  "uuid": "*************-483c-ab90-bd4d18273f13"
}
```

## 🛡️ 错误处理

- 网络连接异常自动重试
- 页面导航失败自动恢复
- 图片下载失败跳过继续
- 详细的日志记录

## 📝 注意事项

1. **登录要求**: 需要手动登录 Nieta Art 账户
2. **网络稳定**: 确保网络连接稳定
3. **存储空间**: 预留足够磁盘空间
4. **运行时间**: 完整爬取可能需要数小时
5. **合理使用**: 请遵守网站使用条款

## 🎉 完成状态

所有要求的功能已完全实现：
- ✅ 分层目录结构 (`dataset/nieta/category/character`)
- ✅ 角色卡片点击和UUID提取
- ✅ 创作页面图片批量下载 (前100张)
- ✅ 完善的统计和错误处理

爬虫现在可以完整地爬取 Nieta Art 网站的角色信息和相关图片！
