{"3": {"inputs": {"model": "depth_anything_v2_vitl_fp32.safetensors"}, "class_type": "DownloadAndLoadDepthAnythingV2Model", "_meta": {"title": "DownloadAndLoadDepthAnythingV2Model"}}, "6": {"inputs": {"value": 2}, "class_type": "JWInteger", "_meta": {"title": "Integer"}}, "10": {"inputs": {"da_model": ["3", 0], "images": ["32", 0]}, "class_type": "DepthAnything_V2", "_meta": {"title": "Depth Anything V2"}}, "11": {"inputs": {"blend_factor": 0.8000000000000002, "blend_mode": "normal", "image1": ["10", 0], "image2": ["14", 0]}, "class_type": "Blend", "_meta": {"title": "Blend"}}, "14": {"inputs": {"detect_hand": "enable", "detect_body": "enable", "detect_face": "enable", "resolution": 512, "scale_stick_for_xinsr_cn": "disable", "image": ["32", 0]}, "class_type": "OpenposePreprocessor", "_meta": {"title": "OpenPose Pose"}}, "15": {"inputs": {"mirror_type": "horizontal", "image": ["37", 0]}, "class_type": "ImageMirror", "_meta": {"title": "Image Mirror"}}, "32": {"inputs": {"aspect_ratio": "original", "proportional_width": 1, "proportional_height": 1, "fit": "letterbox", "method": "lanc<PERSON>s", "round_to_multiple": "8", "scale_to_side": "longest", "scale_to_length": 640, "background_color": "#000000", "image": ["72", 0]}, "class_type": "LayerUtility: ImageScaleByAspectRatio V2", "_meta": {"title": "LayerUtility: ImageScaleByAspectRatio V2"}}, "33": {"inputs": {"select": ["6", 0], "sel_mode": false, "input1": ["15", 0], "input2": ["37", 0]}, "class_type": "ImpactSwitch", "_meta": {"title": "Switch (Any)"}}, "35": {"inputs": {"width": 480, "height": 832, "upscale_method": "nearest-exact", "keep_proportion": false, "divisible_by": 2, "crop": 0, "image": ["33", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image (deprecated)"}}, "37": {"inputs": {"image": "微信图片_20250526145213.png"}, "class_type": "LoadImage", "_meta": {"title": "InputImage"}}, "43": {"inputs": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "WanVideoWrapper_I2V", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": false, "trim_to_audio": false, "pingpong": false, "save_output": true}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "45": {"inputs": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "WanVideoWrapper_I2V", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": false, "trim_to_audio": false, "pingpong": false, "save_output": true}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "46": {"inputs": {"gguf_name": "Wan2.1-VACE-14B-Q5_K_S.gguf"}, "class_type": "LoaderGGUF", "_meta": {"title": "GGUF Loader"}}, "47": {"inputs": {"clip_name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "type": "wan", "device": "default"}, "class_type": "ClipLoaderGGUF", "_meta": {"title": "GGUF CLIP Loader"}}, "48": {"inputs": {"vae_name": "wan_2.1_vae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "49": {"inputs": {"any_01": ["46", 0]}, "class_type": "Any Switch (rgthree)", "_meta": {"title": "Any Switch (rgthree)"}}, "52": {"inputs": {"PowerLoraLoaderHeaderWidget": {"type": "PowerLoraLoaderHeaderWidget"}, "lora_1": {"on": true, "lora": "wan2.1/Wan21_CausVid_14B_T2V_lora_rank32.safetensors", "strength": 0.78}, "➕ Add Lora": "", "model": ["49", 0], "clip": ["47", 0]}, "class_type": "<PERSON> Lora <PERSON> (rgthree)", "_meta": {"title": "<PERSON> Lora <PERSON> (rgthree)"}}, "53": {"inputs": {"text": ["70", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["52", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "54": {"inputs": {"text": "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，三条胳膊，三只手，背景人很多，倒着走，手部模糊，手指分不开", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["52", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "55": {"inputs": {"shift": 6.000000000000001, "model": ["52", 0]}, "class_type": "ModelSamplingSD3", "_meta": {"title": "ModelSamplingSD3"}}, "56": {"inputs": {"width": ["35", 1], "height": ["35", 2], "length": 81, "batch_size": 1, "strength": 1, "positive": ["53", 0], "negative": ["54", 0], "vae": ["48", 0], "control_video": ["11", 0], "reference_image": ["33", 0]}, "class_type": "WanVaceToVideo", "_meta": {"title": "WanVaceToVideo"}}, "57": {"inputs": {"seed": 859911411659283, "steps": 8, "cfg": 1, "sampler_name": "uni_pc", "scheduler": "normal", "denoise": 1, "model": ["55", 0], "positive": ["56", 0], "negative": ["56", 1], "latent_image": ["56", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "58": {"inputs": {"trim_amount": ["56", 3], "samples": ["57", 0]}, "class_type": "TrimVideoLatent", "_meta": {"title": "TrimVideoLatent"}}, "59": {"inputs": {"samples": ["58", 0], "vae": ["48", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "60": {"inputs": {"anything": ["59", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "66": {"inputs": {"filename_prefix": "wan/anime", "fps": 16.000000000000004, "lossless": false, "quality": 90, "method": "default", "images": ["59", 0]}, "class_type": "SaveAnimatedWEBP", "_meta": {"title": "SaveAnimatedWEBP"}}, "68": {"inputs": {"model": "promptgen_base_v2.0", "folder_path": "Path to your image folder", "caption_method": "simple", "max_new_tokens": 1024, "num_beams": 4, "random_prompt": "never", "prefix_caption": "", "suffix_caption": "", "replace_tags": "", "speak_and_recognation": {"__value__": [false, true]}, "images": ["37", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON>_Tagger", "_meta": {"title": "🐾MiaoshouAI Tagger"}}, "69": {"inputs": {"text": "女孩跳舞，头上戴着皇冠", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "Text Multiline", "_meta": {"title": "UserPrompt"}}, "70": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": ["68", 2], "text_b": ",zidanshijian", "text_c": ["69", 0], "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "72": {"inputs": {"video": "input/wan2.1/dance2.mp4", "force_rate": 16, "custom_width": 0, "custom_height": 0, "frame_load_cap": 0, "skip_first_frames": 33, "select_every_nth": 1, "format": "<PERSON>"}, "class_type": "VHS_LoadVideoPath", "_meta": {"title": "Load Video (Path) 🎥🅥🅗🅢"}}}