#!/usr/bin/env python 
# -*- coding:utf-8 -*-
from typing import Optional 
from fastapi import APIRouter, UploadFile, File, Form, Request
import base64
import uuid 
from fastapi.responses import StreamingResponse
import io
import time
import os

from ..config import settings
from ..utils import DecryptRoute
from ..utils import log_csv 
import shutil
import traceback 

from PIL import Image 

from ..utils.cipher_lib import CipherAgent
cipher_tool = CipherAgent().get_cipher()
from ..utils.comfy_api import comfy_image, comfy_edit, comfy_refer, comfy_draw, comfy_t2i, comfy_webp, comfy_wan, encode_pil_to_base64
import uuid

import logging
img_logger = logging.getLogger("img")
root_logger = logging.getLogger("root")

router = APIRouter(prefix='/styles', tags=['风格化接口'])
router.route_class = DecryptRoute

from enum import Enum
class VariantModel(str, Enum):
    comics = "comics"
    technology = "technology"
    lineart = "lineart"
    portrait = "portrait"
    hologram = "hologram"
    hanfu = "hanfu"
    pink = "pink"
    toy = "toy"
    sticker = "sticker"   
    watercolor = "watercolor"
    claymate = "claymate"
    edit = "edit"
    face1 = "face1"
    face2 = "face2"
    face3 = "face3"
    face4 = "face4"        
    face5 = "face5"
    face6 = "face6"
    face7 = "face7"
    face8 = "face8"
    face9 = "face9"        
    face10 = "face10"
    face11 = "face11"
    face12 = "face12"
    face13 = "face13"
    
    #头像风格 全身 脸部更多细节 更像参考任务
    #头像风格，全身，精修脸部，大眼睛，长睫毛，磨皮，加上彩妆, 加饰品， 瘦脸 
    
            
@router.post("/variant",  summary="风格化-post上传文件，本地保存")
async def img2img_style(request:Request, srcImage: UploadFile,
                            style:VariantModel = Form(default=VariantModel.comics), 
                            templateId:str = Form(default="1"), 
                            appVer:str = Form(default="1.0"),
                            page:str = Form(default="ai")):
    """
        风格化接口, 上传文件, 返回生成下载图片
    """
    fprefix = str(uuid.uuid4())
    output = io.BytesIO()
    try:
        model_style = str(style).split(".")[-1]
        data_date = time.strftime("%Y/%m/%d", time.localtime())
        upload_dir = os.path.join(settings.upload_dir, data_date)
        dest_file = str(uuid.uuid4()) + "_" + srcImage.filename 
        os.makedirs(upload_dir, exist_ok=True)
        upload_file = os.path.join(upload_dir, dest_file)
        with open(upload_file, "wb") as buffer:
            shutil.copyfileobj(srcImage.file, buffer)
        
        (imgdata, mime_type, mime_format)  = await comfy_image(upload_file, model_style)
        imgsrc = encode_pil_to_base64(Image.open(upload_file), "png")
        imgdst =  encode_pil_to_base64(imgdata, "png")
        img_logger.info(log_csv([int(time.time()),request.client.host,f"{model_style}", templateId, appVer, page, imgsrc, imgdst]))
        
        imgdata.save(output, format=mime_format, save_all=True)
        output.seek(0)

        headers = {
            'Content-type': mime_type,
            'Content-Disposition': 'attachment; filename="' + f'{fprefix}.{mime_format.lower()}"'
        }
        
    except Exception as e:
        expr = traceback.format_exc()
        root_logger.info(expr)
        
        img = Image.open(os.path.join(settings.comfy_dir, "edit.png"))
        img.save(output, format="PNG")
        output.seek(0)
        mime_type="image/png"
        headers = {
            'Content-type': 'image/png',
            'Content-Disposition': 'attachment; filename="' + fprefix + '.png"'
        }

    return StreamingResponse(content=output, headers=headers, media_type=mime_type)

class LoraModel(str, Enum):
    comics = "comics"
    cyberpunk = "cyberpunk"
    chinese = "chinese"
    anime = "anime"
    cartoon = "cartoon"
    romantic = "romantic"
    portrait = "portrait"

StyleLoraPrompt = {}

StyleLoraPrompt["comics"] = "Comics style, lineart"
StyleLoraPrompt["cyberpunk"] = "Cyberpunk, technology, furture"
StyleLoraPrompt["chinese"] = "Chinese Hanfu"
StyleLoraPrompt["anime"] = "Janpanese Anime, niji"
StyleLoraPrompt["cartoon"] = "American cartoon, Disney, 3D"
StyleLoraPrompt["romantic"] = "Francesca Capaldi,cinematic"
StyleLoraPrompt["portrait"] = "portrait, realism"

@router.post("/edit",  summary="图片编辑-post上传文件，本地保存")
async def img2img_edit(request:Request, srcImage: UploadFile,
                            prompt:str = Form(default="detail portrait"),
                            style:LoraModel = Form(default=LoraModel.portrait), 
                            appVer:str = Form(default="1.0"),
                            factor:str = Form(default="9:16 portrait 768x1344"),
                            detail:int = Form(default=5),
                            seed:int =Form(default=-1),
                            repaint:float=Form(default=0.5),
                            page:str = Form(default="ai")):
    """
        图片编辑, 上传文件, 返回生成下载图片
    """
    fprefix = str(uuid.uuid4())
    output = io.BytesIO()
    try:
        style_lora = str(style).split(".")[-1]
        data_date = time.strftime("%Y/%m/%d", time.localtime())
        upload_dir = os.path.join(settings.upload_dir, data_date)
        dest_file = str(uuid.uuid4()) + "_" + srcImage.filename 
        os.makedirs(upload_dir, exist_ok=True)
        upload_file = os.path.join(upload_dir, dest_file)
        with open(upload_file, "wb") as buffer:
            shutil.copyfileobj(srcImage.file, buffer)
        
        workflow = "edit"
        (imgdata, mime_type, mime_format)  = await comfy_edit(upload_file, workflow,  prompt, StyleLoraPrompt[style_lora], factor, detail, seed, repaint)
        imgsrc = encode_pil_to_base64(Image.open(upload_file), "png")
        imgdst =  encode_pil_to_base64(imgdata, "png")
        img_logger.info(log_csv([int(time.time()),request.client.host,f"{workflow}", style_lora, appVer, page, imgsrc, imgdst]))
        
        imgdata.save(output, format=mime_format, save_all=True)
        output.seek(0)

        headers = {
            'Content-type': mime_type,
            'Content-Disposition': 'attachment; filename="' + f'{fprefix}.{mime_format.lower()}"'
        }
        
    except Exception as e:
        expr = traceback.format_exc()
        root_logger.info(expr)
        
        img = Image.open(os.path.join(settings.comfy_dir, "edit.png"))
        img.save(output, format="PNG")
        output.seek(0)
        mime_type="image/png"
        headers = {
            'Content-type': 'image/png',
            'Content-Disposition': 'attachment; filename="' + fprefix + '.png"'
        }

    return StreamingResponse(content=output, headers=headers, media_type=mime_type)



class KontextModel(str, Enum):
    Integration = "情境深度融合"
    Seamless = "无痕融合"
    Scenery = "场景传送"
    Pan = "移动镜头"
    Relight = "重新布光"
    Product = "专业产品图"
    Zoom = "画面缩放"
    Colorize = "图像上色"
    Poster = "电影海报"
    Comic = "卡通漫画化"
    Textless = "移除文字"
    Hairstyle = "更换发型"
    Muscular = "肌肉猛男化"
    Clear = "清空家具"
    Interior = "室内设计"
    Season = "季节变换"
    TimeTravel = "时光旅人"
    Texture = "材质置换"
    Miniature = "微缩世界"
    Fantasy = "幻想领域"
    Apparel = "衣帽改造"
    Imitate = "艺术风格模仿"
    Blueprint = "蓝图视角"
    Reflection = "添加倒影"
    PixelArt = "像素艺术"
    Sketch = "铅笔手绘"
    OilPainting = "油画风格"

@router.post("/kontext",  summary="图片编辑-post上传文件，本地保存")
async def img2img_kontext(request:Request, srcImage: UploadFile,
                            prompt:str = Form(default="detail portrait"),
                            style:str = Form(default="487"), 
                            appVer:str = Form(default="1.0"),
                            factor:str = Form(default="9:16 portrait 768x1344"),
                            detail:int = Form(default=5),
                            seed:int =Form(default=-1),
                            repaint:float=Form(default=0.5),
                            page:str = Form(default="ai"),
                            template:int = Form(default=13)):
    """
        图片编辑, 上传文件, 返回生成下载图片
    """
    fprefix = str(uuid.uuid4())
    output = io.BytesIO()
    try:
        style_lora = style
        data_date = time.strftime("%Y/%m/%d", time.localtime())
        upload_dir = os.path.join(settings.upload_dir, data_date)

        dest_file = str(uuid.uuid4()) + "_" + srcImage.filename 
        os.makedirs(upload_dir, exist_ok=True)
        upload_file = os.path.join(upload_dir, dest_file)
        with open(upload_file, "wb") as buffer:
            shutil.copyfileobj(srcImage.file, buffer)

        workflow = f"refer_{template}"
        (imgdata, mime_type, mime_format) = await comfy_draw(upload_file, workflow,  prompt, style_lora, factor, detail, seed, repaint)
       
        imgsrc = encode_pil_to_base64(Image.open(upload_file), "png")
        imgdst =  encode_pil_to_base64(imgdata, "png")        
        img_logger.info(log_csv([int(time.time()),request.client.host,f"{workflow}", f"mat_{style_lora}", appVer, page, imgsrc, imgdst]))
        
        imgdata.save(output, format=mime_format, save_all=True)
        output.seek(0)

        headers = {
            'Content-type': mime_type,
            'Content-Disposition': 'attachment; filename="' + f'{fprefix}.{mime_format.lower()}"'
        }
        
    except Exception as e:
        expr = traceback.format_exc()
        root_logger.info(expr)
        
        img = Image.open(os.path.join(settings.comfy_dir, "edit.png"))
        img.save(output, format="PNG")
        output.seek(0)
        mime_type="image/png"
        headers = {
            'Content-type': 'image/png',
            'Content-Disposition': 'attachment; filename="' + fprefix + '.png"'
        }

    return StreamingResponse(content=output, headers=headers, media_type=mime_type)

@router.post("/refimg",  summary="图片编辑-post上传文件，本地保存")
async def img2img_refer(request:Request, srcImage: UploadFile,
                            prompt:str = Form(default="detail portrait"),
                            style:str = Form(default="487"), 
                            appVer:str = Form(default="1.0"),
                            factor:str = Form(default="9:16 portrait 768x1344"),
                            detail:int = Form(default=5),
                            seed:int =Form(default=-1),
                            repaint:float=Form(default=0.5),
                            page:str = Form(default="ai"),
                            template:int = Form(default=13)):
    """
        图片编辑, 上传文件, 返回生成下载图片
    """
    fprefix = str(uuid.uuid4())
    output = io.BytesIO()
    try:
        style_lora = style
        data_date = time.strftime("%Y/%m/%d", time.localtime())
        upload_dir = os.path.join(settings.upload_dir, data_date)
        dest_file = str(uuid.uuid4()) + "_" + srcImage.filename 
        os.makedirs(upload_dir, exist_ok=True)
        upload_file = os.path.join(upload_dir, dest_file)
        with open(upload_file, "wb") as buffer:
            shutil.copyfileobj(srcImage.file, buffer)
        
        workflow = f"refer_{template}"
        (imgdata, mime_type, mime_format) = await comfy_refer(upload_file, workflow,  prompt, style_lora, factor, detail, seed, repaint)

        imgsrc = encode_pil_to_base64(Image.open(upload_file), "png")
        imgdst =  encode_pil_to_base64(imgdata, "png")
        img_logger.info(log_csv([int(time.time()),request.client.host,f"{workflow}", f"mat_{style_lora}", appVer, page, imgsrc, imgdst]))
        
        imgdata.save(output, format=mime_format, save_all=True)
        output.seek(0)

        headers = {
            'Content-type': mime_type,
            'Content-Disposition': 'attachment; filename="' + f'{fprefix}.{mime_format.lower()}"'
        }
        
    except Exception as e:
        expr = traceback.format_exc()
        root_logger.info(expr)
        
        img = Image.open(os.path.join(settings.comfy_dir, "edit.png"))
        img.save(output, format="PNG")
        output.seek(0)
        mime_type="image/png"
        headers = {
            'Content-type': 'image/png',
            'Content-Disposition': 'attachment; filename="' + fprefix + '.png"'
        }

    return StreamingResponse(content=output, headers=headers, media_type=mime_type)

@router.post("/refdraw",  summary="图片编辑-我说你画，本地保存")
async def img2img_refer(request:Request, srcImage: Optional[UploadFile] = File(None),
                            prompt:str = Form(default="detail portrait"),
                            style:str = Form(default="487"), 
                            appVer:str = Form(default="1.0"),
                            factor:str = Form(default="9:16 portrait 768x1344"),
                            detail:int = Form(default=5),
                            seed:int =Form(default=-1),
                            repaint:float=Form(default=0.5),
                            page:str = Form(default="ai"),
                            template:int = Form(default=13)):
    """
        图片编辑, 上传文件, 返回生成下载图片
    """
    fprefix = str(uuid.uuid4())
    output = io.BytesIO()
    try:
        style_lora = style
        data_date = time.strftime("%Y/%m/%d", time.localtime())
        upload_dir = os.path.join(settings.upload_dir, data_date)
        if srcImage:
            dest_file = str(uuid.uuid4()) + "_" + srcImage.filename 
            os.makedirs(upload_dir, exist_ok=True)
            upload_file = os.path.join(upload_dir, dest_file)
            with open(upload_file, "wb") as buffer:
                shutil.copyfileobj(srcImage.file, buffer)
        else:
            upload_file = os.path.join(settings.upload_dir, "dft_img/0_result.png")

        workflow = f"refer_{template}"
        if srcImage:
            (imgdata, mime_type, mime_format) = await comfy_draw(upload_file, workflow,  prompt, style_lora, factor, detail, seed, repaint)
        else:
            (imgdata, mime_type, mime_format) = await comfy_t2i(upload_file, workflow,  prompt, style_lora, factor, detail, seed, repaint)

        imgsrc = encode_pil_to_base64(Image.open(upload_file), "png")
        imgdst =  encode_pil_to_base64(imgdata, "png")        
        img_logger.info(log_csv([int(time.time()),request.client.host,f"{workflow}", f"mat_{style_lora}", appVer, page, imgsrc, imgdst]))
        
        imgdata.save(output, format=mime_format, save_all=True)
        output.seek(0)

        headers = {
            'Content-type': mime_type,
            'Content-Disposition': 'attachment; filename="' + f'{fprefix}.{mime_format.lower()}"'
        }
        
    except Exception as e:
        expr = traceback.format_exc()
        root_logger.info(expr)
        
        img = Image.open(os.path.join(settings.comfy_dir, "edit.png"))
        img.save(output, format="PNG")
        output.seek(0)
        mime_type="image/png"
        headers = {
            'Content-type': 'image/png',
            'Content-Disposition': 'attachment; filename="' + fprefix + '.png"'
        }

    return StreamingResponse(content=output, headers=headers, media_type=mime_type)

@router.post("/refwan",  summary="图片编辑-图转视频，本地保存")
async def img2img_wan(request:Request, srcImage: UploadFile,
                            prompt:str = Form(default="detail portrait"),
                            style:str = Form(default="487"), 
                            appVer:str = Form(default="1.0"),
                            factor:str = Form(default="9:16 portrait 768x1344"),
                            detail:int = Form(default=5),
                            seed:int =Form(default=-1),
                            repaint:float=Form(default=0.5),
                            page:str = Form(default="ai"),
                            template:int = Form(default=13)):
    """
        图片编辑, 上传文件, 返回生成下载图片
    """
    fprefix = str(uuid.uuid4())
    output = io.BytesIO()
    try:
        style_lora = style
        data_date = time.strftime("%Y/%m/%d", time.localtime())
        upload_dir = os.path.join(settings.upload_dir, data_date)
        dest_file = str(uuid.uuid4()) + "_" + srcImage.filename 
        os.makedirs(upload_dir, exist_ok=True)
        upload_file = os.path.join(upload_dir, dest_file)
        with open(upload_file, "wb") as buffer:
            shutil.copyfileobj(srcImage.file, buffer)
        
        workflow = f"refer_{template}"
        (imgdata, mime_type, mime_format) = await comfy_wan(upload_file, workflow,  prompt, style_lora, factor, detail, seed, repaint)
        imgsrc = encode_pil_to_base64(Image.open(upload_file), "png")
        imgdst =  encode_pil_to_base64(imgdata, "png")        
        img_logger.info(log_csv([int(time.time()),request.client.host,f"{workflow}", f"mat_{style_lora}", appVer, page, imgsrc, imgdst]))
        
        imgdata.save(output, format=mime_format, save_all=True)
        output.seek(0)

        headers = {
            'Content-type': mime_type,
            'Content-Disposition': 'attachment; filename="' + f'{fprefix}.{mime_format.lower()}"'
        }
        
    except Exception as e:
        expr = traceback.format_exc()
        root_logger.info(expr)
        
        img = Image.open(os.path.join(settings.comfy_dir, "edit.png"))
        img.save(output, format="PNG")
        output.seek(0)
        mime_type="image/png"
        headers = {
            'Content-type': 'image/png',
            'Content-Disposition': 'attachment; filename="' + fprefix + '.png"'
        }

    return StreamingResponse(content=output, headers=headers, media_type=mime_type)

@router.post("/refwebp",  summary="测试保存webp图片")
async def img2img_webp(request:Request, srcImage: UploadFile):
    """
       测试保存webp图片
    """
    fprefix = str(uuid.uuid4())
    output = io.BytesIO()
    try:
        data_date = time.strftime("%Y/%m/%d", time.localtime())
        upload_dir = os.path.join(settings.upload_dir, data_date)
        dest_file = str(uuid.uuid4()) + "_" + srcImage.filename 
        os.makedirs(upload_dir, exist_ok=True)
        upload_file = os.path.join(upload_dir, dest_file)
        with open(upload_file, "wb") as buffer:
            shutil.copyfileobj(srcImage.file, buffer)
        
        workflow = f"test_webp"
        (imgdata, mime_type, mime_format) = await comfy_webp(upload_file, workflow)
        
        imgdata.save(output, format=mime_format, save_all=True)
        output.seek(0)

        headers = {
            'Content-type': mime_type,
            'Content-Disposition': 'attachment; filename="' + f'{fprefix}.{mime_format.lower()}"'
        }
        
    except Exception as e:
        expr = traceback.format_exc()
        root_logger.info(expr)
        
        img = Image.open(os.path.join(settings.comfy_dir, "edit.png"))
        img.save(output, format="PNG")
        output.seek(0)
        mime_type="image/png"
        headers = {
            'Content-type': 'image/png',
            'Content-Disposition': 'attachment; filename="' + fprefix + '.png"'
        }

    return StreamingResponse(content=output, headers=headers, media_type=mime_type)
