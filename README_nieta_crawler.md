# Nieta Art 角色爬虫

这个爬虫用于从 Nieta Art 网站爬取角色信息并下载角色图片。

## 功能特性

- 🎭 **角色信息提取**: 从 Nieta Art 发现页面提取角色信息
- 📁 **自动目录创建**: 为每个角色在 `dataset/nieta` 下创建独立目录
- 🖼️ **图片下载**: 自动下载角色图片到对应目录
- 🏷️ **智能命名**: 使用角色名称命名目录和文件
- 📊 **分类支持**: 支持多个角色分类的爬取
- 📈 **进度统计**: 提供详细的爬取和下载统计信息

## 目录结构

爬取完成后，会在项目根目录下创建以下结构：

```
dataset/
└── nieta/
    ├── 角色名称1/
    │   └── 角色名称1.jpg
    ├── 角色名称2/
    │   └── 角色名称2.png
    └── ...
```

## 使用方法

### 1. 安装依赖

确保已安装以下 Python 包：
- `playwright`
- `aiohttp`
- `aiofiles`

```bash
pip install playwright aiohttp aiofiles
```

### 2. 运行爬虫

```bash
python spider/nieta_art_crawler.py
```

### 3. 手动登录

- 爬虫会自动打开浏览器窗口
- 请在浏览器中手动登录 Nieta Art 账户
- 登录完成后，在终端按 Enter 键继续

### 4. 等待完成

爬虫会自动：
- 导航到发现页面
- 获取所有分类
- 逐个分类爬取角色信息
- 下载角色图片到对应目录
- 生成统计报告

## 输出文件

- `nieta_all_characters.json`: 包含所有角色信息的 JSON 文件
- `dataset/nieta/`: 包含所有角色图片的目录结构

## 配置选项

可以在 `spider/nieta_art_crawler.py` 中修改以下配置：

- `DATASET_DIR`: 数据集保存目录（默认: "dataset/nieta"）
- `categories[:5]`: 爬取的分类数量（默认: 前5个分类）
- `range(2)`: 每个分类的滚动次数（默认: 2次）

## 文件名处理

爬虫会自动处理角色名称中的特殊字符：
- 移除或替换不合法的文件名字符 (`<>:"/\\|?*`)
- 处理空白字符
- 限制文件名长度（最大100字符）
- 为空名称提供默认值

## 错误处理

- 网络错误时会自动重试
- 下载失败的图片会在统计中标记
- 详细的错误日志记录

## 注意事项

1. **登录要求**: 需要有效的 Nieta Art 账户
2. **网络连接**: 确保网络连接稳定
3. **存储空间**: 确保有足够的磁盘空间存储图片
4. **使用频率**: 请合理控制爬取频率，避免对服务器造成过大压力

## 测试

运行测试脚本验证功能：

```bash
python test_nieta_crawler.py
```

## 故障排除

### 常见问题

1. **浏览器启动失败**
   - 确保已安装 Playwright 浏览器: `playwright install`

2. **图片下载失败**
   - 检查网络连接
   - 确认图片 URL 有效性

3. **目录创建失败**
   - 检查磁盘空间
   - 确认写入权限

### 日志查看

爬虫会输出详细的日志信息，包括：
- 页面导航状态
- 角色提取进度
- 图片下载状态
- 错误信息

## 更新日志

### v2.0
- ✅ 添加图片下载功能
- ✅ 添加目录自动创建
- ✅ 添加文件名清理
- ✅ 添加下载统计
- ✅ 改进错误处理

### v1.0
- ✅ 基础角色信息爬取
- ✅ 分类支持
- ✅ JSON 数据导出
