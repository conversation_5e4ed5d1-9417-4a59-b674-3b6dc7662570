{"71": {"inputs": {"image": "微信图片_20250610170337.png"}, "class_type": "LoadImage", "_meta": {"title": "参考图1"}}, "72": {"inputs": {"image": "微信图片_20250523151857.jpg"}, "class_type": "LoadImage", "_meta": {"title": "InputImage"}}, "73": {"inputs": {"aspect_ratio": "original", "proportional_width": 1, "proportional_height": 1, "fit": "letterbox", "method": "lanc<PERSON>s", "round_to_multiple": "None", "scale_to_side": "height", "scale_to_length": 1024, "background_color": "#000000", "image": ["76", 0]}, "class_type": "LayerUtility: ImageScaleByAspectRatio V2", "_meta": {"title": "LayerUtility: ImageScaleByAspectRatio V2"}}, "74": {"inputs": {"aspect_ratio": "original", "proportional_width": 1, "proportional_height": 1, "fit": "letterbox", "method": "lanc<PERSON>s", "round_to_multiple": "None", "scale_to_side": "height", "scale_to_length": 1024, "background_color": "#000000", "image": ["78", 0]}, "class_type": "LayerUtility: ImageScaleByAspectRatio V2", "_meta": {"title": "LayerUtility: ImageScaleByAspectRatio V2"}}, "76": {"inputs": {"padding": 0, "padding_percent": 0.8000000000000002, "index": 0, "analysis_models": ["77", 0], "image": ["183", 0]}, "class_type": "FaceBoundingBox", "_meta": {"title": "Face Bounding Box"}}, "77": {"inputs": {"library": "insightface", "provider": "CUDA"}, "class_type": "FaceAnalysisModels", "_meta": {"title": "Face Analysis Models"}}, "78": {"inputs": {"padding": 0, "padding_percent": 1.0000000000000002, "index": 0, "analysis_models": ["77", 0], "image": ["135", 0]}, "class_type": "FaceBoundingBox", "_meta": {"title": "Face Bounding Box"}}, "79": {"inputs": {"left": 0, "top": 0, "right": 8, "bottom": 0, "feathering": 0, "image": ["73", 0]}, "class_type": "ImagePadForOutpaint", "_meta": {"title": "Pad Image for Outpainting"}}, "80": {"inputs": {"left": 8, "top": 0, "right": 0, "bottom": 0, "feathering": 0, "image": ["74", 0]}, "class_type": "ImagePadForOutpaint", "_meta": {"title": "Pad Image for Outpainting"}}, "81": {"inputs": {"mask": ["79", 1]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "82": {"inputs": {"mask": ["80", 1]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "83": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["81", 0], "image2": ["82", 0]}, "class_type": "ImageConcanate", "_meta": {"title": "Image Concatenate"}}, "84": {"inputs": {"channel": "red", "image": ["83", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}, "85": {"inputs": {"lama_model": "lama", "device": "cuda", "invert_mask": false, "mask_grow": 48, "mask_blur": 24, "image": ["150", 0], "mask": ["84", 0]}, "class_type": "LayerUtility: <PERSON><PERSON><PERSON>", "_meta": {"title": "LayerUtility: <PERSON><PERSON><PERSON>(Advance)"}}, "86": {"inputs": {"images": ["85", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "117": {"inputs": {"PowerLoraLoaderHeaderWidget": {"type": "PowerLoraLoaderHeaderWidget"}, "lora_1": {"on": true, "lora": "wan2.1/Wan21_AccVid_T2V_14B_lora_rank32_fp16.safetensors", "strength": 2}, "➕ Add Lora": "", "model": ["120", 0], "clip": ["119", 0]}, "class_type": "<PERSON> Lora <PERSON> (rgthree)", "_meta": {"title": "<PERSON> Lora <PERSON> (rgthree)"}}, "118": {"inputs": {"vae_name": "Wan2_1_VAE_bf16.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "119": {"inputs": {"clip_name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "type": "wan", "device": "default"}, "class_type": "CLIPLoader", "_meta": {"title": "Load CLIP"}}, "120": {"inputs": {"gguf_name": "Wan2.1-VACE-14B-Q4_K_S.gguf"}, "class_type": "LoaderGGUF", "_meta": {"title": "GGUF Loader"}}, "121": {"inputs": {"text": "背景为绿色公园，背景清晰", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "Text Multiline", "_meta": {"title": "UserPrompt"}}, "122": {"inputs": {"text": ["186", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["117", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "123": {"inputs": {"text": "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，三条胳膊，三只手，背景人很多，倒着走，看不到脸，三个人，多个人，背景模糊", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["117", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "124": {"inputs": {"width": 832, "height": 464, "length": 81, "batch_size": 1, "strength": 1.0000000000000002, "positive": ["122", 0], "negative": ["123", 0], "vae": ["118", 0], "reference_image": ["85", 0]}, "class_type": "WanVaceToVideo", "_meta": {"title": "WanVaceToVideo"}}, "125": {"inputs": {"shift": 6.000000000000001, "model": ["117", 0]}, "class_type": "ModelSamplingSD3", "_meta": {"title": "ModelSamplingSD3"}}, "126": {"inputs": {"seed": 163241715988076, "steps": 8, "cfg": 1, "sampler_name": "uni_pc", "scheduler": "simple", "denoise": 1, "model": ["125", 0], "positive": ["124", 0], "negative": ["124", 1], "latent_image": ["124", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "127": {"inputs": {"trim_amount": ["124", 3], "samples": ["126", 0]}, "class_type": "TrimVideoLatent", "_meta": {"title": "TrimVideoLatent"}}, "128": {"inputs": {"samples": ["127", 0], "vae": ["118", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "129": {"inputs": {"anything": ["128", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "135": {"inputs": {"rem_mode": "RMBG-1.4", "image_output": "Preview", "save_prefix": "ComfyUI", "torchscript_jit": false, "add_background": "white", "refine_foreground": false, "images": ["71", 0]}, "class_type": "easy imageRemBg", "_meta": {"title": "Image Remove Bg"}}, "139": {"inputs": {"filename_prefix": "ComfyUI", "fps": 16.000000000000004, "lossless": true, "quality": 90, "method": "default", "images": ["128", 0]}, "class_type": "SaveAnimatedWEBP", "_meta": {"title": "SaveAnimatedWEBP"}}, "150": {"inputs": {"direction": "right", "match_image_size": false, "image1": ["79", 0], "image2": ["80", 0]}, "class_type": "ImageConcanate", "_meta": {"title": "Image Concatenate"}}, "183": {"inputs": {"rem_mode": "RMBG-1.4", "image_output": "Preview", "save_prefix": "ComfyUI", "torchscript_jit": false, "add_background": "white", "refine_foreground": false, "images": ["72", 0]}, "class_type": "easy imageRemBg", "_meta": {"title": "Image Remove Bg"}}, "184": {"inputs": {"string_a": ["121", 0], "string_b": ["185", 0]}, "class_type": "ConcatStringSingle", "_meta": {"title": "Concat String (Single) 📅🅕🅝"}}, "185": {"inputs": {"text": "a person and another person standing side by side, facing forward, full body, realistic lighting, natural background, detailed clothing, photo-realistic, cinematic style,   \nthen facing each other, close interaction, hugging each other tightly, a person burying his head in the another person's shoulder, intimate body language, warm lighting, tender mood, soft focus, realistic human skin texture, expressive faces, high detail, romantic moment, heartwarming scene, natural pose, 4k, ultra-detailed, no kissing, no lip contact", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "Text Multiline", "_meta": {"title": "UserPrompt1"}}, "186": {"inputs": {"text_0": "背景为绿色公园，背景清晰a person and another person standing side by side, facing forward, full body, realistic lighting, natural background, detailed clothing, photo-realistic, cinematic style,   \nthen facing each other, close interaction, hugging each other tightly, a person burying his head in the another person's shoulder, intimate body language, warm lighting, tender mood, soft focus, realistic human skin texture, expressive faces, high detail, romantic moment, heartwarming scene, natural pose, 4k, ultra-detailed, no kissing, no lip contact", "text": ["184", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}}