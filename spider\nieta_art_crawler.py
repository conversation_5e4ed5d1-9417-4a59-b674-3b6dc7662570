import asyncio
from playwright.async_api import async_playwright
import logging
import json
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 目标 URL
TARGET_URL = "https://app.nieta.art"
DISCOVER_URL = "https://app.nieta.art/character/discover"

async def extract_character_info(page, category="默认"):
    """从发现页面提取角色信息"""
    characters = []
    
    # 等待角色卡片加载
    try:
        await page.wait_for_selector('.grid.grid-cols-1', timeout=10000)
    except:
        logger.warning("页面加载超时，继续提取数据")
    
    # 获取页面HTML内容
    html_content = await page.content()
    
    # 使用正则表达式提取角色信息
    import re
    
    # 匹配角色卡片的模式
    card_pattern = r'<div class="relative bg-#FFF/5[^"]*"[^>]*style="[^"]*">.*?<img class="object-cover[^"]*"[^>]*src="([^"]*?)".*?<div class="relative px-8px mb-6px line-clamp-2">([^<]*?)</div>.*?<div class="text-#FFF text-9px leading-11px font-600">([^<]*?)</div>'
    
    # 查找所有匹配的角色卡片
    matches = re.findall(card_pattern, html_content, re.DOTALL)
    
    logger.info(f"通过正则表达式在 {category} 分类中找到 {len(matches)} 个角色卡片")
    
    for img_url, name, heat_score in matches:
        try:
            character_info = {
                "category": category,
                "name": name.strip(),
                "image_url": img_url,
                "heat_score": int(heat_score) if heat_score.isdigit() else 0
            }
            characters.append(character_info)
        except Exception as e:
            logger.error(f"处理角色信息时出错: {e}")
            continue
    
    # 如果正则表达式没有找到结果，尝试另一种方法
    if len(characters) == 0:
        logger.info(f"正则表达式在 {category} 分类中未找到结果，尝试使用选择器方法")
        
        # 获取所有角色卡片 - 使用更通用的选择器
        try:
            character_cards = await page.query_selector_all('[data-index] .relative')
            
            logger.info(f"在 {category} 分类中找到 {len(character_cards)} 个角色卡片")
            
            for card in character_cards:
                try:
                    # 提取角色名称
                    name_element = await card.query_selector('.line-clamp-2')
                    name = await name_element.inner_text() if name_element else "未知"
                    
                    # 提取图片URL
                    img_element = await card.query_selector('img.object-cover')
                    img_url = await img_element.get_attribute('src') if img_element else ""
                    
                    # 提取热度分数
                    heat_element = await card.query_selector('.text-9px.leading-11px.font-600')
                    heat_score = await heat_element.inner_text() if heat_element else "0"
                    
                    if name != "未知" and name.strip() != "":  # 只有当名称有效时才添加
                        character_info = {
                            "category": category,
                            "name": name.strip(),
                            "image_url": img_url,
                            "heat_score": int(heat_score) if heat_score.isdigit() else 0
                        }
                        characters.append(character_info)
                        
                except Exception as e:
                    logger.error(f"提取角色信息时出错: {e}")
                    continue
        except Exception as e:
            logger.error(f"获取角色卡片时出错: {e}")
    
    return characters

async def get_categories(page):
    """获取所有分类"""
    categories = []
    
    try:
        # 获取所有分类元素
        category_elements = await page.query_selector_all('.flex.flex-col .shrink-0.relative')
        
        logger.info(f"找到 {len(category_elements)} 个分类")
        
        for element in category_elements:
            try:
                # 提取分类名称
                name_element = await element.query_selector('.relative.text-12px.leading-18px.max-w-64.truncate')
                if name_element:
                    name = await name_element.inner_text()
                    if name and name.strip() != "":
                        categories.append(name.strip())
            except Exception as e:
                logger.error(f"提取分类名称时出错: {e}")
                continue
                
    except Exception as e:
        logger.error(f"获取分类时出错: {e}")
    
    # 如果没有获取到分类，使用默认分类
    if len(categories) == 0:
        categories = ["默认"]
        
    logger.info(f"最终获取到的分类: {categories}")
    return categories

async def crawl_nieta_art():
    """使用 Playwright 爬取 Nieta Art 网站内容"""
    async with async_playwright() as p:
        # 启动浏览器 (默认使用 Chromium)
        browser = await p.chromium.launch(headless=False)  # headless=False 以便用户可以看到浏览器并进行登录操作
        page = await browser.new_page()
        
        # 导航到目标页面
        logger.info(f"正在导航到: {TARGET_URL}")
        await page.goto(TARGET_URL)
        
        # 等待用户手动登录
        logger.info("请在浏览器中手动登录...")
        logger.info("登录完成后，请在终端中按 Enter 键继续...")
        input("按 Enter 键继续...")
        
        # 登录后获取页面信息
        logger.info("正在获取页面信息...")
        title = await page.title()
        current_url = page.url
        
        # 这里可以添加更多爬取逻辑
        # 例如获取特定元素的内容等
        logger.info(f"页面标题: {title}")
        logger.info(f"当前URL: {current_url}")
        
        # 导航到发现页面
        logger.info(f"正在导航到发现页面: {DISCOVER_URL}")
        await page.goto(DISCOVER_URL)
        await page.wait_for_load_state('networkidle') # 等待网络空闲，确保页面加载完成

        # 可以在这里添加更多针对发现页面的爬取逻辑
        # 例如，获取发现页面的内容、图片链接等
        discover_title = await page.title()
        logger.info(f"发现页面标题: {discover_title}")
        
        # 获取所有分类
        logger.info("正在获取所有分类...")
        categories = await get_categories(page)
        
        # 存储所有角色信息
        all_characters = []
        
        # 遍历每个分类提取数据
        for i, category in enumerate(categories[:5]):  # 限制前5个分类以节省时间
            logger.info(f"正在处理第 {i+1} 个分类: {category}")
            
            # 如果不是第一个分类，需要点击分类
            if i > 0:
                try:
                    # 查找并点击分类
                    category_elements = await page.query_selector_all('.flex.flex-col .shrink-0.relative')
                    if i < len(category_elements):
                        await category_elements[i].click()
                        await page.wait_for_timeout(2000)  # 等待页面加载
                except Exception as e:
                    logger.error(f"点击分类 {category} 时出错: {e}")
                    continue
            
            # 提取当前分类的角色信息
            logger.info(f"正在提取 {category} 分类的角色信息...")
            characters = await extract_character_info(page, category)
            all_characters.extend(characters)
            
            # 滚动加载更多内容
            logger.info(f"检查 {category} 分类是否需要滚动加载更多内容...")
            previous_count = len(characters)
            
            # 尝试滚动几次以加载更多内容
            for j in range(2):  # 减少滚动次数以节省时间
                # 滚动到页面底部
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                # 等待新内容加载
                await page.wait_for_timeout(2000)
                
                # 再次提取角色信息
                new_characters = await extract_character_info(page, category)
                
                # 只统计当前分类的新角色
                current_category_chars = [c for c in new_characters if c.get("category") == category]
                if len(current_category_chars) > previous_count:
                    logger.info(f"第 {j+1} 次滚动后发现新内容，{category} 分类总共 {len(current_category_chars)} 个角色")
                    characters = current_category_chars
                    previous_count = len(characters)
                else:
                    logger.info(f"第 {j+1} 次滚动后 {category} 分类没有发现新内容")
                    break
        
        # 保存所有数据到JSON文件
        output_file = "nieta_all_characters.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(all_characters, f, ensure_ascii=False, indent=2)
        
        # 按分类统计
        category_stats = {}
        for character in all_characters:
            category = character.get("category", "未知")
            if category in category_stats:
                category_stats[category] += 1
            else:
                category_stats[category] = 1
        
        logger.info(f"已保存 {len(all_characters)} 个角色信息到 {output_file}")
        logger.info("各分类角色数量统计:")
        for category, count in category_stats.items():
            logger.info(f"  {category}: {count} 个角色")
        
        # 关闭浏览器
        await browser.close()
        logger.info("爬取完成!")

if __name__ == "__main__":
    asyncio.run(crawl_nieta_art())
