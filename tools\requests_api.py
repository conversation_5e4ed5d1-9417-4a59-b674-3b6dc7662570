#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import sys, getopt
from PIL import Image
from pathlib import Path
import os
import argparse
import oss2
import requests
from datetime import datetime 
import pymysql
from pymysql.cursors import DictCursor
import traceback
import time 
import hashlib
import subprocess
import re 
from random import randint 

#os.environ["HTTP_PROXY"]= "http://127.0.0.1:1080"
#os.environ["HTTPS_PROXY"]= "http://127.0.0.1:1080"

ai_api="http://10.9.0.251:8765/api/v1/styles"

OSS_ENDPOINT="http://oss-cn-beijing-internal.aliyuncs.com"
OSS_BUCKET="cdn-buckeet"
OSS_CDN_PREFIX="https://cdn.banyunjuhe.com"
OSS_SECRET_ID="LTAI5tRFHhUZAyjm3ZJsMAuB"
OSS_SECRET_KEY="******************************"
OSS_BASE_PATH="isplimg"

DEFAULT_IMAGE_MD5 = ["3c0080e8f6661c3b033aae049cbb3ced","2a6994d45d36683b8cd7879013898be4","17a483f1d7429e62330dd367b87ba5ba","ecbfb9e98bbd0e9eec58e818e09b08f2",
                     "6e1c075d05b37c48c4f92efb81cf6f70","5b0ba321ef9883d66686f7c5ae58a3e1","601f38c9dc9e89d1db7f59e6f94660b9","2b03193736d0a115e8f8fa32d4acb3b3",
                     "7903a91ce353c5c0b3be7b5417f3d908","fccc01e05acea5087ff7e397f0e1093e","8060be9823b9a8cde346b0a6e8b3aa87","b33c978cfe461045072d5b6830b46691"]

mail_to="<EMAIL>,<EMAIL>,<EMAIL>"

def get_now():
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def send_mail(msg:str):
    print(msg)
    os.system(f"/root/miniconda3/bin/python /root/bin/mail.py {mail_to} 'AI生图接口错误' 'Dear,\n {msg}'") 

def parse_args():
    desc = "prepare microscope dataset"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--data_date', type=str, default='', help='current datetime')
    return parser.parse_args()

def md5(dt):
    md5 = hashlib.md5()
    md5.update(dt)
    md5text = md5.hexdigest().lower()
    return md5text

def is_default_image(img_path):
    data = open(img_path, "rb").read()
    rs = md5(data)
    
    return rs in DEFAULT_IMAGE_MD5

def get_conn(host="rm-2zez7x3o3wp4096bm.mysql.rds.aliyuncs.com", port=3306, user="crius", passwd="Crius!@##@!", database="fuxi"):
    try:
        conn = pymysql.connect(host=host, port=port, user=user, passwd=passwd, db=database, charset="utf8", autocommit=True,  cursorclass=DictCursor)
        return conn
    except Exception as e:
        #exstr = traceback.format_exc()
        #print(exstr)
        return None

def exec_sql(sql:str, params:list):
    conn = get_conn()
    if conn is None:
        send_mail(f"{get_now()} 获取数据库连接失败.请及时关注. \n执行sql: {sql}, 参数: {params}")
        return
    cur = conn.cursor()
    cur.execute(sql, params)
    rowcnt = cur.rowcount
    if rowcnt < 1:
        send_mail(f"{get_now()} 更新数据库未生效.请及时关注. \n执行sql: {sql}, 参数: {params}")
    cur.close()
    conn.close()
    print(f"{get_now()} 执行sql: {sql}, 参数: {params}, 影响行数: {rowcnt}")
    return

def extract_file_ext(response):
    # Extract filename from Content-Disposition header
    content_disposition = response.headers.get("Content-Disposition")
    filename = None
    if content_disposition:
        match = re.search(r'filename\*?=(?:UTF-8\'\')?["\']?([^"\';]+)', content_disposition)
        if match:
            filename = match.group(1)
    file_ext = "png"
    if filename:
        file_ext = filename.split(".")[-1]
    
    return file_ext

def process_ai_request(api_url:str, img_path:str, params:dict):
      
    files = {'srcImage': open(img_path, 'rb').read()}
    print(api_url, params)
    res = requests.post(api_url, data=params, files=files,  timeout=1200)

    if res.status_code != 200:
        return None
    
    file_ext = extract_file_ext(res)
    local_img_path = img_path.split("_")[0]
    local_img_path += "_result." + file_ext
    with open(local_img_path, 'wb') as f:
        f.write(res.content)
    return local_img_path

def upload_oss(img_path:str, year:str, month:str, day:str):

    timestamp = int(datetime.now().timestamp() * 1000)
    ext = os.path.splitext(img_path)[1]
    oss_path = f"{OSS_BASE_PATH}/{year}/{month}/{day}/{timestamp}{ext}"
    upload_time = 0
    upload_status = False
    
    while True:
        if upload_time > 5 and upload_status == False:
            send_mail(f"{get_now()} 上传{img_path} 到 oss://{OSS_BUCKET}{oss_path} 失败.请及时关注.")
            break
        if upload_status == True:
            break
        try:
            upload_time += 1
            bucket = oss2.Bucket(oss2.Auth(OSS_SECRET_ID, OSS_SECRET_KEY), OSS_ENDPOINT, OSS_BUCKET)
            bucket.put_object_from_file(oss_path, img_path)
            upload_status = True
        except Exception as e:
            #expr = traceback.format_exc()
            #print(f"{get_now()} {expr}")
            upload_status = False
            time.sleep(5)
    if upload_status == True:
        return f"{OSS_CDN_PREFIX}/{oss_path}"
    else:
        return None

def process_after_generate(ai_img_path, local_img_path, result, year, month, day):
    
    if not ai_img_path:
        print(f"{get_now()} process image {local_img_path} failed.")
        send_mail(f"{get_now()} 生成任务{result['id']}, 生成图片{local_img_path}返回码非200.本次执行跳过.")
        exec_sql("""update isplimg_ai_image set build_status = %s, status = %s where id = %s """, ["0", "0", result['id']])
        return 
        
    if is_default_image(ai_img_path):
        print(f"{get_now()} process image {local_img_path} return default image.")
        send_mail(f"{get_now()} 生成任务{result['id']}, 生成图片{local_img_path}为默认图,本次执行已返回,请留心关注.")
        matid = result["material_delivery_id"]
        if matid:
            ai_img_path = f"./dataset/dft_img/{matid}_result.png"
            if not os.path.exists(ai_img_path):
                ai_img_path = f"./dataset/dft_img/0_result.png"
                
        cdn_img_path = upload_oss(ai_img_path, year, month, day)
        if cdn_img_path:
            exec_sql("""update isplimg_ai_image set build_url = %s, build_status = %s, status = %s, is_default = %s where id = %s and audit_status = "1" """, [cdn_img_path, "1", "1", "1", result['id']])
            
    else:
        cdn_img_path = upload_oss(ai_img_path, year, month, day)
        if cdn_img_path:
            exec_sql("""update isplimg_ai_image set build_url = %s, build_status = %s, status = %s where id = %s and audit_status = "1" """, [cdn_img_path, "1", "1", result['id']])

def process_default_generate(result, year, month, day):
    
    #ai_img_path = f"./dataset/dft_img/0_result.png"    
    #cdn_img_path = upload_oss(ai_img_path, year, month, day)
    cdn_img_path = "https://cdn.banyunjuhe.com/isplimg/2025/05/30/1748589766125.png"
    if cdn_img_path:
        exec_sql("""update isplimg_ai_image set build_url = %s, build_status = %s, status = %s where id = %s and audit_status = "1" """, [cdn_img_path, "1", "1", result['id']])

def get_image_factor(factor:str, local_img_path:str):
    
    ratioDict = {
        "1:1":"1:1 square 1024x1024",
        "3:4":"3:4 portrait 896x1152",
        "5:8":"5:8 portrait 832x1216",
        "9:16":"9:16 portrait 768x1344",
        "9:21":"9:21 portrait 640x1536",
        "4:3":"4:3 landscape 1152x896",
        "3:2":"3:2 landscape 1216x832",
        "16:9":"16:9 landscape 1344x768",
        "21:9":"21:9 landscape 1536x640"   
    }
    
    if factor in ratioDict:
        return ratioDict[factor]
    else:
        # Get image dimensions using PIL
        img = Image.open(local_img_path)
        width, height = img.size
        img.close()
        
        # Return appropriate ratio based on dimensions
        if width == height:
            return "1:1 square 1024x1024"
        elif width > height:
            if width/height >= 2.3:
                return "21:9 landscape 1536x640"
            elif width/height >= 1.7:
                return "16:9 landscape 1344x768" 
            elif width/height >= 1.4:
                return "3:2 landscape 1216x832"
            else:
                return "4:3 landscape 1152x896"
        else:
            if height/width >= 2.3:
                return "9:21 portrait 640x1536"
            elif height/width >= 1.7:
                return "9:16 portrait 768x1344"
            elif height/width >= 1.4:
                return "5:8 portrait 832x1216"
            else:
                return "3:4 portrait 896x1152"
            
    
def get_image_detail(detail:str):

    dnum = int(detail)  
    if dnum < 1 or dnum > 10:
        dnum = 5
    return detail

def get_image_style(style_id:str):

#重新整理的风格提示词
# 1 唯美动漫风 comics (style of Cyril Rolando:1.2)
# 2 赛博朋克 cyberpunk (style of Bastien Lecouffe-Deharme:1.2)
# 3 新中式 chinese (style of Huang Guangjian)
# 4 二次元 anime (style of Yusuke Murata)
# 5 漫画 cartoon （style of Sui Ishida）
# 6 法式浪漫 romantic (style of Ross Tran)
# 7 写实人像 portrait (style of Slim Aarons)
    
    snum = int(style_id)
    if snum == 1:
        return "comics"
    elif snum == 2:
        return "cyberpunk"
    elif snum == 3:
        return "chinese"
    elif snum == 4:
        return "anime"
    elif snum == 5:
        return "cartoon"
    elif snum == 6:
        return "romantic"
    elif snum == 7:
        return "portrait"
    
    return "portrait"

def get_image_seed(seed):
    if not seed:
        return -1
    elif seed < -1:
        return -1
    else:
        return seed
    
def get_image_repaint(repaint):
    if not repaint:
        return 0.75
    elif repaint < 0 or repaint > 1:
        return 0.75
    else:
        repaint = repaint * 0.5 + 0.5
        return repaint    
    
def get_refer_repaint(repaint):
    if not repaint:
        return 1.0
    elif repaint < 0 or repaint > 1:
        return 1.0
    else:
        repaint = repaint * 0.1 + 0.9
        return repaint

def download_image(result:dict, temp_dir:str):
    
    upload_img = requests.get(result["upload_url"])
    if upload_img.status_code != 200:
        send_mail(f"{get_now()} 生成{result['id']}, 获取图片失败 {result['upload_url']},CDN错误.本次执行跳过.")
        exec_sql("""update isplimg_ai_image set build_status = %s, status = %s where id = %s """, ["0", "0", result['id']])
 
    file_tail = result["upload_url"].split("/")[-1]
    local_file = f"{result['id']}_{file_tail}"
    local_img_path = f"{temp_dir}/{local_file}"
    with open(local_img_path, 'wb') as f:
        f.write(upload_img.content)

    try:
        image = Image.open(local_img_path)
        image.close()
    except Exception as e:
        local_file_tmp = local_file.replace(".", "_1.")
        os.system(f"docker run --rm -v {temp_dir}:/imgs dpokidov/imagemagick:7.1.1-41-bullseye /imgs/{local_file} /imgs/{local_file_tmp}")
        local_img_path = f"{temp_dir}/{local_file_tmp}"
        if not os.path.exists(local_img_path):
            image = Image.new('RGB', (512, 512), color = 'white')
            image.save(local_img_path)
            image.close()
        
    print(f"{get_now()} download image for {result['id']}, url {result['upload_url']}")
   
    return local_img_path
        
def process_variant_request(result:dict, data_date:str):

    year = data_date[0:4]
    month = data_date[4:6]
    day = data_date[6:8]
    temp_dir = f"./dataset/{year}/{month}/{day}"
    Path(temp_dir).mkdir(parents=True, exist_ok=True)
    local_img_path = download_image(result, temp_dir)
   
    try: 
        data = {"style":result["template_code"], "templateId":result["template_id"], "appVer":result["app_ver"], "page":"ai"}
        ai_img_path = process_ai_request(ai_api + "/variant", local_img_path, data)

        print(f"{get_now()} process image {local_img_path}, local image {ai_img_path}")
        
        process_after_generate(ai_img_path, local_img_path, result, year, month, day)
        
    except Exception as e:
        expr = traceback.format_exc()
        send_mail(f"{get_now()} 生成任务{result['id']}, {local_img_path}的图片任务失败.\n" + expr) 
        exec_sql("""update isplimg_ai_image set build_status = %s, status = %s where id = %s """, ["0", "0", result['id']])

def process_edit_request(result:dict, data_date:str):
    
    year = data_date[0:4]
    month = data_date[4:6]
    day = data_date[6:8]
    temp_dir = f"./dataset/{year}/{month}/{day}"
    Path(temp_dir).mkdir(parents=True, exist_ok=True)
    local_img_path = download_image(result, temp_dir)
   
    try: 
        
        factor = get_image_factor(result["image_scale"], local_img_path)
        detail = get_image_detail(result["picture_detail"])
        style = get_image_style(result["style_id"])
        seed = get_image_seed(result["random_seed"])
        repaint = get_image_repaint(result["repaint"])
        
        data = {"model":result['template_code'],  
                "appVer":result["app_ver"], "page":"ai", "prompt":result["desc"], 
                "factor": factor, "detail": detail, "style":style, 
                "seed":seed, "repaint": repaint}
        
        ai_img_path = process_ai_request(ai_api + "/edit", local_img_path, data)
        
        print(f"{get_now()} process image {local_img_path}, local image {ai_img_path}")
        
        process_after_generate(ai_img_path, local_img_path, result, year, month, day)
        
    except Exception as e:
        expr = traceback.format_exc()
        send_mail(f"{get_now()} 生成任务{result['id']}, {local_img_path}的图片任务失败.\n" + expr) 
        exec_sql("""update isplimg_ai_image set build_status = %s, status = %s where id = %s """, ["0", "0", result['id']])

def process_refer_request(result:dict, data_date:str):
    
    year = data_date[0:4]
    month = data_date[4:6]
    day = data_date[6:8]
    temp_dir = f"./dataset/{year}/{month}/{day}"
    Path(temp_dir).mkdir(parents=True, exist_ok=True)
    local_img_path = download_image(result, temp_dir)
    
    try: 
        
        factor = get_image_factor(result["image_scale"], local_img_path)
        detail = get_image_detail(result["picture_detail"])
        style = result["material_delivery_id"]
        seed = get_image_seed(result["random_seed"])
        repaint = get_refer_repaint(result["repaint"])
        template = result["style_id"]
        if template in [14, 15]:
            template = 13
            
        data = {"model":result['template_code'],  
                "appVer":result["app_ver"], "page":"ai", "prompt":result["desc"], 
                "factor": factor, "detail": detail, "style":style, 
                "seed":seed, "repaint": repaint, "template":template}
        
        ai_img_path = process_ai_request(ai_api + "/refimg", local_img_path, data)
        
        print(f"{get_now()} process image {local_img_path}, local image {ai_img_path}")
        
        process_after_generate(ai_img_path, local_img_path, result, year, month, day)
        
    except Exception as e:
        expr = traceback.format_exc()
        send_mail(f"{get_now()} 生成任务{result['id']}, {local_img_path}的图片任务失败.\n" + expr) 
        exec_sql("""update isplimg_ai_image set build_status = %s, status = %s where id = %s """, ["0", "0", result['id']])

def process_draw_request(result:dict, data_date:str):
    
    year = data_date[0:4]
    month = data_date[4:6]
    day = data_date[6:8]
    temp_dir = f"./dataset/{year}/{month}/{day}"
    Path(temp_dir).mkdir(parents=True, exist_ok=True)
    local_img_path = download_image(result, temp_dir)
     
    try: 
        
        factor = get_image_factor(result["image_scale"], local_img_path)
        detail = get_image_detail(result["picture_detail"])
        style = result["material_delivery_id"]
        seed = get_image_seed(result["random_seed"])
        repaint = get_refer_repaint(result["repaint"])
        template = result["style_id"]
            
        data = {"model":result['template_code'],  
                "appVer":result["app_ver"], "page":"ai", "prompt":result["desc"], 
                "factor": factor, "detail": detail, "style":style, 
                "seed":seed, "repaint": repaint, "template":template}
        
        ai_img_path = process_ai_request(ai_api + "/refdraw", local_img_path, data)
        
        print(f"{get_now()} process image {local_img_path}, local image {ai_img_path}")
        
        process_after_generate(ai_img_path, local_img_path, result, year, month, day)
        
    except Exception as e:
        expr = traceback.format_exc()
        send_mail(f"{get_now()} 生成任务{result['id']}, {local_img_path}的图片任务失败.\n" + expr) 
        exec_sql("""update isplimg_ai_image set build_status = %s, status = %s where id = %s """, ["0", "0", result['id']])

def process_kontext_request(result:dict, data_date:str):
    
    year = data_date[0:4]
    month = data_date[4:6]
    day = data_date[6:8]
    temp_dir = f"./dataset/{year}/{month}/{day}"
    Path(temp_dir).mkdir(parents=True, exist_ok=True)
    local_img_path = download_image(result, temp_dir)
     
    try: 
        
        factor = get_image_factor(result["image_scale"], local_img_path)
        detail = get_image_detail(result["picture_detail"])
        style = result["material_delivery_id"]
        seed = get_image_seed(result["random_seed"])
        repaint = get_refer_repaint(result["repaint"])
        template = result["style_id"]
            
        data = {"model":result['template_code'],  
                "appVer":result["app_ver"], "page":"ai", "prompt":result["desc"], 
                "factor": factor, "detail": detail, "style":style, 
                "seed":seed, "repaint": repaint, "template":template}
        
        ai_img_path = process_ai_request(ai_api + "/kontext", local_img_path, data)
        
        print(f"{get_now()} process image {local_img_path}, local image {ai_img_path}")
        
        process_after_generate(ai_img_path, local_img_path, result, year, month, day)
        
    except Exception as e:
        expr = traceback.format_exc()
        send_mail(f"{get_now()} 生成任务{result['id']}, {local_img_path}的图片任务失败.\n" + expr) 
        exec_sql("""update isplimg_ai_image set build_status = %s, status = %s where id = %s """, ["0", "0", result['id']])

def process_wan_request(result:dict, data_date:str):
    
    year = data_date[0:4]
    month = data_date[4:6]
    day = data_date[6:8]
    temp_dir = f"./dataset/{year}/{month}/{day}"
    Path(temp_dir).mkdir(parents=True, exist_ok=True)
    
    local_img_path = download_image(result, temp_dir)
    
    try: 
        
        factor = get_image_factor(result["image_scale"], local_img_path)
        detail = get_image_detail(result["picture_detail"])
        style = result["material_delivery_id"]
        seed = get_image_seed(result["random_seed"])
        repaint = get_refer_repaint(result["repaint"])
        template = result["style_id"]
            
        data = {"model":result['template_code'],  
                "appVer":result["app_ver"], "page":"ai", "prompt":result["desc"], 
                "factor": factor, "detail": detail, "style":style, 
                "seed":seed, "repaint": repaint, "template":template}
        
        ai_img_path = process_ai_request(ai_api + "/refwan", local_img_path, data)
        
        print(f"{get_now()} process image {local_img_path}, local image {ai_img_path}")
        
        process_after_generate(ai_img_path, local_img_path, result, year, month, day)
        
    except Exception as e:
        expr = traceback.format_exc()
        send_mail(f"{get_now()} 生成任务{result['id']}, {local_img_path}的图片任务失败.\n" + expr) 
        exec_sql("""update isplimg_ai_image set build_status = %s, status = %s where id = %s """, ["0", "0", result['id']])
        
def process_ai_record(data_date:str):

    conn = get_conn()
    if conn is None:
        send_mail(f"{get_now()} 获取数据库连接失败.请及时关注.")
        return

    date_start = data_date[0:4] + "-" + data_date[4:6] + "-" + data_date[6:] + " 00:00:00"
    date_end = data_date[0:4] + "-" + data_date[4:6] + "-" + data_date[6:] + " 23:59:59"

    cur = conn.cursor()
    sql = """select count(*) as cnt from isplimg_ai_image 
        where upload_time >= %s and build_status = '-1' """
    cur.execute(sql, (date_start,))
    cnt = cur.fetchone()
    cur.close()
    if cnt['cnt'] > 6:
        send_mail(f"{get_now()} 当前生图队列排队数为{cnt['cnt']},本次执行跳过,请及时关注")
        return 
        
    cur = conn.cursor()
    sql = """select a.*, b.code as template_code from isplimg_ai_image a left join dim_sys b on b.id = a.template_id 
        where a.upload_time >= %s and a.upload_time <= %s and a.build_status = '0' and audit_status = '1' order by a.id LIMIT 1 """
    cur.execute(sql, (date_start, date_end))
    result = cur.fetchone()
    cur.close()
    if result == None:
        return 
    conn.close()

    exec_sql("""update isplimg_ai_image set build_status = %s where id = %s """, ["-1", result['id']])
    channel = result["channel"]
    # 自动化测试渠道, 抽样5%做生图处理
    # if result["app_ver"] == "2.2.50":
    if channel == "4000":
        rnum = randint(1, 100)
        if rnum >= 5:
            print(f"{get_now()} 自动化测试渠道, 本次抽样未中, 跳过执行生图任务.")
            process_default_generate(result, data_date[0:4], data_date[4:6], data_date[6:8])
            return
    
    if result["style_id"] == 8:
        process_variant_request(result, data_date)
    elif result["style_id"] >= 10 and result["style_id"] <= 18:
        process_refer_request(result, data_date)

    elif result["style_id"] == 60:
        process_draw_request(result, data_date)
    elif (result["style_id"] >= 19 and result["style_id"] <= 32) or (result["style_id"] >= 53 \
        and result["style_id"] <= 55) or (result["style_id"] >= 57 and result["style_id"] <= 58) \
        or (result["style_id"] >= 62 and result["style_id"] <= 91):
        process_kontext_request(result, data_date)
    elif (result["style_id"] >= 33 and result["style_id"] <= 52) or result["style_id"] == 56 or result["style_id"] == 59 or result["style_id"] == 61:
        process_wan_request(result, data_date)
    # /edit	自由编辑	用 LoraModel + 手写 prompt + 可调参数（factor/detail/seed/repaint）
    else:
        process_edit_request(result, data_date)

def main():
    args = parse_args()
    data_date = args.data_date
    if data_date == "":
        data_date = datetime.now().strftime("%Y%m%d")
    print(data_date)

    # 检查是否有超过10分钟的AI生图任务
    cmd = subprocess.Popen(["ps", "-eo", "pid,etime,cmd"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    cmd_out, cmd_err = cmd.communicate()
    output_str = cmd_out.decode('utf-8')
    error_str = cmd_err.decode('utf-8')
    
    out_lines = output_str.split("\n")

    service_stack = False
    for out_line in out_lines:
        out_fields = re.split(r"\s+", out_line)
        if len(out_fields) < 4:
            continue
        cmd = ",".join(out_fields[3:])
        if cmd.find("requests_api") < 0:
            continue
        pid = out_fields[0]
        etime = out_fields[1]
        if etime.find(":") == -1:
            continue
        etime_arr = etime.split(":")
        if len(etime_arr) == 3:
            service_stack = True
            os.system(f"kill -9 {pid}")
        elif len(etime_arr) == 2:
            if int(etime_arr[0]) > 30:
                service_stack = True
                os.system(f"kill -9 {pid}")

    if service_stack == True:
        docker_image = "banyunjuhe-comfyui-new" 
        cmd = subprocess.Popen(["docker", "exec",  docker_image,  "pgrep",  "-f",  "python"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        cmd_out, cmd_err = cmd.communicate()
        output_str = cmd_out.decode('utf-8')
        error_str = cmd_err.decode('utf-8')
        
        out_lines = output_str.split("\n")
        for pid in out_lines:
            if pid.isdigit():
                os.system(f"docker exec {docker_image} kill -9 {pid}") 
        os.system(f"docker exec {docker_image} /workspace/ComfyUI/start.sh")
        
        send_mail(f"{get_now()} 当前生图任务执行时间已有超过10分钟的请求,执行重启服务操作.")
        return
            
    process_ai_record(data_date)

if __name__ == "__main__":
    main()

